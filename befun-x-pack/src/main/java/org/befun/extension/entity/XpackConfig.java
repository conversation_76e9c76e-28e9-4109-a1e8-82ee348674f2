package org.befun.extension.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.befun.extension.constant.XPackAppType;
import org.hibernate.annotations.Where;

import javax.persistence.*;


@Entity
@Getter
@Setter
@Where(clause = "enabled = 1")
@Table(name = "xpack_config")
@DtoClass
public class XpackConfig extends BaseEntity {

    @Column(name = "enabled")
    @Schema(description = "是否开启")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private Boolean enabled;

    @Column(name = "app")
    @Schema(description = "项目所属")
    private String app;

    @Column(name = "type")
    @Schema(description = "配置类型")
    @Enumerated(value = EnumType.STRING)
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private XPackAppType type;

    @Column(name = "sub_type")
    @Schema(description = "配置子类型")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String subType;

    @Column(name = "config")
    @Schema(description = "配置内容 调用者自行控制")
    @DtoProperty(jsonView = ResourceViews.Detail.class)
    private String config;
}